# 五子棋游戏 (Gobang Game)

## 简介 (Introduction)

这是一个使用Flutter + Rust技术栈开发的传统中式风格五子棋游戏。采用无禁手规则，支持人人对战模式，具有古典围棋的视觉风格。

**技术栈：**
- 前端：Flutter 3.8+ (Dart)
- 后端逻辑：Rust
- 跨语言通信：flutter_rust_bridge 2.10.0
- 状态管理：GetX
- UI适配：flutter_screenutil

**开发环境：**
- Flutter SDK 3.8+
- Rust 1.70+
- Windows/macOS/Linux 桌面平台

**代码架构：**
- `lib/controllers/` - GetX状态管理控制器
- `lib/widgets/` - UI组件
- `lib/pages/` - 页面
- `rust/src/api/` - Rust游戏逻辑核心

## 功能 (Feature)

- [x] 15x15标准棋盘
- [x] 无禁手五子棋规则
- [x] 人人对战模式
- [x] 传统中式木质纹理界面
- [x] 黑白棋子质感渲染
- [x] 实时游戏状态显示
- [x] 悔棋功能
- [x] 重新开始游戏
- [x] 坐标显示开关
- [x] 音效开关 (预留)
- [x] 动画效果开关
- [x] 精确的棋子位置对齐
- [x] 响应式棋盘尺寸适配
- [x] 完整棋盘显示（修复垂直截断问题）
- [x] 动态窗口大小适配
- [ ] 人机对战模式 (AI)
- [ ] 机机对战模式 (AI vs AI)
- [ ] 游戏记录保存/加载
- [ ] 网络对战功能

## 待办事项 (TODO)

- 实现AI对战算法 (Minimax + Alpha-Beta剪枝)
- 添加游戏音效
- 实现游戏记录功能
- 添加网络对战支持
- 优化UI动画效果
- 添加更多棋盘主题

## 笔记 (Note)

### 游戏规则
- 采用无禁手五子棋规则，黑白双方轮流落子
- 率先在横、竖、斜任一方向连成5子者获胜
- 棋盘为15x15标准尺寸，天元和星位标记

### 技术要点
- 使用flutter_rust_bridge实现Flutter与Rust的无缝通信
- Rust端负责游戏逻辑核心，确保性能和准确性
- Flutter端负责UI渲染和用户交互
- GetX提供响应式状态管理
- 4:1布局比例 (棋盘:信息面板)
- 响应式布局设计，支持动态窗口大小调整
- 智能棋盘尺寸计算，确保完整显示15x15网格
- 详细技术实现参见：[UI实现文档](.doc/ui_implementation.md)

### 项目结构
```
lib/
├── controllers/        # GetX控制器
├── widgets/           # UI组件
├── pages/            # 页面
└── rust/             # Rust桥接代码

rust/src/
├── api/              # API接口
│   ├── gobang.rs     # 五子棋核心逻辑
│   └── mod.rs        # 模块声明
└── lib.rs           # 库入口
```

## 参考 (Reference)

- [Flutter官方文档](https://docs.flutter.dev/)
- [Rust官方文档](https://doc.rust-lang.org/)
- [flutter_rust_bridge文档](https://cjycode.com/flutter_rust_bridge/)
- [GetX状态管理](https://github.com/jonataslaw/getx)
- [五子棋规则说明](https://zh.wikipedia.org/wiki/五子棋)

## 协议 (License)

MIT License
