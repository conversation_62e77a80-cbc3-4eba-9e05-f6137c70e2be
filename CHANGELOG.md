# 更新日志 (Changelog)

本文档记录项目的所有重要变更。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [1.0.2] - 2025-06-09

### 修复 (Fixed)
- 修复棋盘垂直截断问题，现在能完整显示15x15网格
- 修复withOpacity deprecated警告，使用withValues替代
- 优化Windows构建问题，确保项目能正常编译运行

### 改进 (Improved)
- 调整设计尺寸从1200x800到1400x900，提供更合理的宽高比
- 实现动态棋盘尺寸计算，支持不同窗口大小自适应
- 移除强制AspectRatio约束，让棋盘能适应父容器
- 清理调试代码，提升生产环境代码质量

### 技术变更 (Technical Changes)
- 使用LayoutBuilder动态计算可用空间和棋盘尺寸
- 优化棋盘绘制逻辑，确保正方形显示
- 改进点击事件处理，使用正确的尺寸计算
- 添加布局测试文件，便于验证修复效果

### 新增 (Added)
- 添加test/layout_test.dart - 布局计算验证测试
- 添加integration_test/gobang_layout_test.dart - 完整布局集成测试

## [1.0.1] - 2025-01-27

### 修复 (Fixed)
- 修复棋子显示位置不准确问题
- 修复棋子大小过小问题
- 修复GetX嵌套Obx警告
- 优化棋子位置计算，使用LayoutBuilder获取实际绘制区域
- 修复点击坐标计算与棋子位置不一致问题
- 将GobangBoard改为StatefulWidget以支持context访问

### 改进 (Improved)
- 棋子大小现在为格子的80%，更加美观
- 棋子位置精确对齐到交叉点
- 优化了棋盘绘制和交互逻辑
- 改进了调试日志输出

### 技术变更 (Technical Changes)
- 重构`_buildPieces`方法为`_buildPiecesWidget`
- 使用LayoutBuilder动态计算棋盘尺寸
- 统一棋盘绘制和点击事件的坐标系统
- 优化GetX响应式更新机制

## [1.0.0] - 2025-06-09

### 新增 (Added)
- 初始版本发布
- 实现15x15标准五子棋棋盘
- 无禁手五子棋规则引擎 (Rust实现)
- 人人对战模式
- 传统中式木质纹理界面设计
- 黑白棋子质感渲染效果
- 实时游戏状态显示
- 悔棋功能
- 重新开始游戏功能
- 坐标显示开关
- 音效开关 (预留接口)
- 动画效果开关
- 4:1布局比例 (棋盘:信息面板)
- Flutter + Rust跨语言架构
- GetX状态管理
- flutter_screenutil响应式适配
- 完整的单元测试覆盖

### 技术实现 (Technical)
- 使用flutter_rust_bridge 2.10.0实现跨语言通信
- Rust端游戏逻辑核心，确保性能和准确性
- Flutter端UI渲染和用户交互
- 响应式状态管理 (GetX)
- 自定义绘制棋盘和棋子
- 异步游戏状态更新

### 文件结构 (File Structure)
```
lib/
├── controllers/gobang_controller.dart    # 游戏状态控制器
├── widgets/gobang_board.dart            # 棋盘组件
├── widgets/game_info_panel.dart         # 信息面板组件
├── pages/gobang_game_page.dart          # 主游戏页面
└── main.dart                           # 应用入口

rust/src/api/
├── gobang.rs                           # 五子棋核心逻辑
└── mod.rs                             # 模块声明

test/
└── gobang_test.dart                    # 游戏逻辑测试
```

### 已知问题 (Known Issues)
- 测试环境下Rust库加载需要先构建项目
- 部分UI警告 (withOpacity deprecated)

### 计划功能 (Planned Features)
- AI对战模式 (Minimax算法)
- 游戏记录保存/加载
- 网络对战功能
- 更多视觉主题
