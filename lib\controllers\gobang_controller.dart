import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../rust/api/gobang.dart';

/// 五子棋游戏控制器
class GobangController extends GetxController {
  late GobangGame _game;

  // 响应式状态
  final RxInt currentPlayerIndex = 1.obs; // 1: 黑子, 2: 白子 (黑子先手)
  final RxInt gameStateIndex = 0.obs; // 0: 游戏中, 1: 黑子胜, 2: 白子胜, 3: 平局
  final RxInt moveCount = 0.obs;
  final RxBool isGameOver = false.obs;

  // 棋盘状态 - 使用二维数组存储棋子状态
  final RxList<RxList<int>> board = <RxList<int>>[].obs;

  // 游戏设置
  final RxBool showCoordinates = true.obs;
  final RxBool enableSound = true.obs;

  @override
  void onInit() {
    super.onInit();
    _initBoardSync(); // 先同步初始化棋盘
    initGame();
  }

  /// 同步初始化棋盘 (避免UI错误)
  void _initBoardSync() {
    board.clear();
    for (int i = 0; i < 15; i++) {
      final row = <int>[].obs;
      for (int j = 0; j < 15; j++) {
        row.add(0); // 0: 空, 1: 黑子, 2: 白子
      }
      board.add(row);
    }
  }

  /// 初始化游戏
  void initGame() async {
    _game = createGobangGame();
    await _updateGameState();
    await _initBoard();
  }

  /// 初始化棋盘
  Future<void> _initBoard() async {
    final size = await _game.getBoardSize();
    board.clear();
    for (int i = 0; i < size; i++) {
      final row = <int>[].obs;
      for (int j = 0; j < size; j++) {
        row.add(0); // 0: 空, 1: 黑子, 2: 白子
      }
      board.add(row);
    }
  }

  /// 更新游戏状态
  Future<void> _updateGameState() async {
    final currentPlayer = await _game.getCurrentPlayer();
    currentPlayerIndex.value = _pieceTypeToIndex(currentPlayer);

    final gameState = await _game.getGameState();
    gameStateIndex.value = _gameStateToIndex(gameState);

    moveCount.value = await _game.getMoveCount();

    isGameOver.value = gameStateIndex.value != 0;
  }

  /// 棋子类型转换为索引
  int _pieceTypeToIndex(PieceType pieceType) {
    switch (pieceType) {
      case PieceType.black:
        return 1;
      case PieceType.white:
        return 2;
      case PieceType.empty:
        return 0;
    }
  }

  /// 游戏状态转换为索引
  int _gameStateToIndex(GameState gameState) {
    switch (gameState) {
      case GameState.playing:
        return 0;
      case GameState.blackWin:
        return 1;
      case GameState.whiteWin:
        return 2;
      case GameState.draw:
        return 3;
    }
  }

  /// 落子
  Future<bool> makeMove(int row, int col) async {
    debugPrint('makeMove called: row=$row, col=$col, isGameOver=${isGameOver.value}');

    if (isGameOver.value) return false;

    final success = await _game.makeMove(row: row, col: col);
    debugPrint('makeMove result: success=$success');

    if (success) {
      // 更新棋盘显示
      final piece = await _game.getPiece(row: row, col: col);
      final pieceIndex = _pieceTypeToIndex(piece);
      debugPrint('Piece at ($row, $col): $piece -> index: $pieceIndex');

      board[row][col] = pieceIndex;
      debugPrint('Board updated: board[$row][$col] = $pieceIndex');

      // 更新游戏状态
      await _updateGameState();
    }

    return success;
  }

  /// 重置游戏
  Future<void> resetGame() async {
    await _game.reset();
    await _updateGameState();
    await _initBoard();
  }

  /// 悔棋
  Future<bool> undoMove() async {
    if (moveCount.value == 0) return false;

    final success = await _game.undoMove();
    if (success) {
      // 重新同步棋盘状态
      await _syncBoardFromGame();
      await _updateGameState();
    }

    return success;
  }

  /// 从游戏引擎同步棋盘状态
  Future<void> _syncBoardFromGame() async {
    final size = await _game.getBoardSize();
    for (int i = 0; i < size; i++) {
      for (int j = 0; j < size; j++) {
        final piece = await _game.getPiece(row: i, col: j);
        board[i][j] = _pieceTypeToIndex(piece);
      }
    }
  }

  /// 获取当前玩家名称
  String get currentPlayerName {
    return currentPlayerIndex.value == 1 ? '黑子' : '白子';
  }

  /// 获取游戏状态文本
  String get gameStateText {
    switch (gameStateIndex.value) {
      case 0:
        return '轮到 $currentPlayerName';
      case 1:
        return '黑子获胜！';
      case 2:
        return '白子获胜！';
      case 3:
        return '平局！';
      default:
        return '';
    }
  }

  /// 获取棋盘大小 (同步版本，用于UI)
  int get boardSize => 15; // 默认15x15棋盘

  /// 切换坐标显示
  void toggleCoordinates() {
    showCoordinates.value = !showCoordinates.value;
  }

  /// 切换音效
  void toggleSound() {
    enableSound.value = !enableSound.value;
  }
}
